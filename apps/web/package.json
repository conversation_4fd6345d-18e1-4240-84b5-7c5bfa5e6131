{"name": "web", "version": "1.0.0", "type": "module", "private": true, "scripts": {"dev": "next dev --port 3001 --turbopack", "build": "next build", "start": "next start", "lint": "next lint --max-warnings 0", "check-types": "tsc --noEmit"}, "dependencies": {"@repo/ui": "workspace:*", "next": "^15.4.2", "react": "^19.1.0", "react-dom": "^19.1.0"}, "devDependencies": {"@next/eslint-plugin-next": "^15.4.6", "@repo/eslint-config": "workspace:*", "@repo/tailwind-config": "workspace:*", "@repo/typescript-config": "workspace:*", "@tailwindcss/postcss": "^4.1.5", "@types/node": "^22.15.30", "@types/react": "^19.1.0", "@types/react-dom": "^19.1.1", "autoprefixer": "^10.4.20", "eslint": "^9.33.0", "postcss": "^8.5.3", "tailwindcss": "^4.1.5", "typescript": "5.9.2"}}