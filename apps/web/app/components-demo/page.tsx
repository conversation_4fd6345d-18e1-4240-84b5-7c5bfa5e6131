"use client"

import { <PERSON><PERSON> } from "@repo/ui/button"
import { Input } from "@repo/ui/input"
import { Label } from "@repo/ui/label"
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@repo/ui/card-ui"
import { Form, FormField, FormLabel, FormControl } from "@repo/ui/form"
import { Icons } from "@repo/ui/icons"

export default function ComponentsDemo() {
  return (
    <div className="container mx-auto p-8 space-y-8">
      <h1 className="text-3xl font-bold mb-8">shadcn/ui Components Demo</h1>
      
      {/* Buttons */}
      <Card>
        <CardHeader>
          <CardTitle>Buttons</CardTitle>
          <CardDescription>Different button variants and sizes</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex flex-wrap gap-4">
            <Button>Default</Button>
            <Button variant="secondary">Secondary</Button>
            <Button variant="outline">Outline</Button>
            <Button variant="ghost">Ghost</Button>
            <Button variant="link">Link</Button>
            <Button variant="destructive">Destructive</Button>
          </div>
          <div className="flex flex-wrap gap-4">
            <Button size="sm">Small</Button>
            <Button size="default">Default</Button>
            <Button size="lg">Large</Button>
            <Button size="icon">
              <Icons.mail className="h-4 w-4" />
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Inputs */}
      <Card>
        <CardHeader>
          <CardTitle>Inputs</CardTitle>
          <CardDescription>Form inputs with labels</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="email">Email</Label>
            <Input id="email" type="email" placeholder="Enter your email" />
          </div>
          <div className="space-y-2">
            <Label htmlFor="password">Password</Label>
            <Input id="password" type="password" placeholder="Enter your password" />
          </div>
          <div className="space-y-2">
            <Label htmlFor="disabled">Disabled Input</Label>
            <Input id="disabled" placeholder="This is disabled" disabled />
          </div>
        </CardContent>
      </Card>

      {/* Form Example */}
      <Card>
        <CardHeader>
          <CardTitle>Form Example</CardTitle>
          <CardDescription>A complete form using the form components</CardDescription>
        </CardHeader>
        <CardContent>
          <Form>
            <FormField>
              <FormLabel htmlFor="name">Full Name</FormLabel>
              <FormControl>
                <Input id="name" placeholder="John Doe" />
              </FormControl>
            </FormField>
            
            <FormField>
              <FormLabel htmlFor="email-form">Email Address</FormLabel>
              <FormControl>
                <Input id="email-form" type="email" placeholder="<EMAIL>" />
              </FormControl>
            </FormField>
            
            <FormField>
              <FormLabel htmlFor="message">Message</FormLabel>
              <FormControl>
                <textarea 
                  id="message"
                  className="flex min-h-[80px] w-full rounded-md border border-gray-300 bg-white px-3 py-2 text-sm placeholder:text-gray-500 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                  placeholder="Your message here..."
                />
              </FormControl>
            </FormField>
            
            <Button type="submit" className="w-full">
              Submit Form
            </Button>
          </Form>
        </CardContent>
      </Card>

      {/* Icons */}
      <Card>
        <CardHeader>
          <CardTitle>Icons</CardTitle>
          <CardDescription>Available icons from Lucide React</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex flex-wrap gap-4">
            <div className="flex items-center gap-2">
              <Icons.mail className="h-5 w-5" />
              <span>Mail</span>
            </div>
            <div className="flex items-center gap-2">
              <Icons.lock className="h-5 w-5" />
              <span>Lock</span>
            </div>
            <div className="flex items-center gap-2">
              <Icons.eye className="h-5 w-5" />
              <span>Eye</span>
            </div>
            <div className="flex items-center gap-2">
              <Icons.eyeOff className="h-5 w-5" />
              <span>Eye Off</span>
            </div>
            <div className="flex items-center gap-2">
              <Icons.arrowRight className="h-5 w-5" />
              <span>Arrow Right</span>
            </div>
            <div className="flex items-center gap-2">
              <Icons.spinner className="h-5 w-5 animate-spin" />
              <span>Spinner</span>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
