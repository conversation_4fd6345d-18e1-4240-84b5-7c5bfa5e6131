"use client"

import { LoginForm } from "@repo/ui/login-form"

export default function LoginPage() {
  const handleLogin = (data: { email: string; password: string }) => {
    console.log("Login attempt:", data)
    // Here you would typically handle the login logic
    // For example, call an API endpoint, validate credentials, etc.
  }

  const handleForgotPassword = () => {
    console.log("Forgot password clicked")
    // <PERSON><PERSON> forgot password logic
  }

  return (
    <div className="min-h-screen flex">
      {/* Left side - Image/Background */}
      <div className="hidden lg:flex lg:w-1/2 bg-gray-900 relative">
        <div className="absolute inset-0 bg-gradient-to-br from-gray-800 to-gray-900" />
        <div className="relative z-10 flex flex-col justify-center items-center text-white p-12">
          <div className="max-w-md text-center">
            <h1 className="text-4xl font-bold mb-4">
              ONE PLATFORM FOR ALL PROJECT DOCUMENTATION
            </h1>
            <h2 className="text-3xl font-semibold mb-6">
              Clarity, Control, Collaboration
            </h2>
            <p className="text-lg text-gray-300">
              The Most Reliable Platform for Seamless Project File Management
            </p>
          </div>
        </div>
        {/* Ship image placeholder - you can replace this with your actual ship image */}
        <div className="absolute bottom-0 left-0 right-0 h-1/2 bg-gradient-to-t from-gray-700 to-transparent opacity-50" />
      </div>

      {/* Right side - Login Form */}
      <div className="w-full lg:w-1/2 flex items-center justify-center p-8 bg-gray-50">
        <LoginForm
          onSubmit={handleLogin}
          onForgotPassword={handleForgotPassword}
          title="PMS Asset Builder Dashboard Log In"
          className="w-full max-w-md"
        />
      </div>
    </div>
  )
}
