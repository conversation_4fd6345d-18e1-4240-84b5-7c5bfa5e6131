{"name": "@repo/ui", "version": "0.0.0", "sideEffects": ["**/*.css"], "files": ["dist"], "exports": {"./styles.css": "./dist/index.css", "./button": "./dist/button.js", "./input": "./dist/input.js", "./label": "./dist/label.js", "./card-ui": "./dist/card-ui.js", "./form": "./dist/form.js", "./icons": "./dist/icons.js", "./login-form": "./dist/login-form.js", "./lib/utils": "./dist/lib/utils.js", "./*": "./dist/*.js"}, "license": "MIT", "scripts": {"build:styles": "tailwindcss -i ./src/styles.css -o ./dist/index.css", "build:components": "tsc", "check-types": "tsc --noEmit", "dev:styles": "tailwindcss -i ./src/styles.css -o ./dist/index.css --watch", "dev:components": "tsc --watch", "lint": "eslint src --max-warnings 0"}, "peerDependencies": {"react": "^19"}, "devDependencies": {"@repo/eslint-config": "workspace:*", "@repo/tailwind-config": "workspace:*", "@repo/typescript-config": "workspace:*", "@tailwindcss/cli": "^4.1.5", "@types/react": "^19.1.0", "eslint": "^9.33.0", "tailwindcss": "^4.1.5", "typescript": "5.9.2"}, "dependencies": {"@radix-ui/react-label": "^2.1.7", "@radix-ui/react-slot": "^1.2.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lucide-react": "^0.542.0", "tailwind-merge": "^3.3.1"}}