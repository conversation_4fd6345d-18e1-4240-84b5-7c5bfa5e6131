{"name": "@repo/ui", "version": "0.0.0", "sideEffects": ["**/*.css"], "files": ["dist"], "exports": {"./styles.css": "./dist/index.css", "./*": "./dist/*.js"}, "license": "MIT", "scripts": {"build:styles": "tailwindcss -i ./src/styles.css -o ./dist/index.css", "build:components": "tsc", "check-types": "tsc --noEmit", "dev:styles": "tailwindcss -i ./src/styles.css -o ./dist/index.css --watch", "dev:components": "tsc --watch", "lint": "eslint src --max-warnings 0"}, "peerDependencies": {"react": "^19"}, "devDependencies": {"@repo/eslint-config": "workspace:*", "@repo/tailwind-config": "workspace:*", "@repo/typescript-config": "workspace:*", "@tailwindcss/cli": "^4.1.5", "@types/react": "^19.1.0", "eslint": "^9.33.0", "tailwindcss": "^4.1.5", "typescript": "5.9.2"}}