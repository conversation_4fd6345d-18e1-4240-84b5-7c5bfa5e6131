import * as React from "react";
import { cn } from "./lib/utils";
import { Button } from "./button";
import { Input } from "./input";
import { Label } from "./label";
import { Card, CardContent, CardHeader, CardTitle } from "./card-ui";
import { Icons } from "./icons";

export interface LoginFormProps
  extends Omit<React.HTMLAttributes<HTMLDivElement>, "onSubmit"> {
  onSubmit?: (data: { email: string; password: string }) => void;
  isLoading?: boolean;
  title?: string;
  subtitle?: string;
  showForgotPassword?: boolean;
  onForgotPassword?: () => void;
}

const LoginForm = React.forwardRef<HTMLDivElement, LoginFormProps>(
  (
    {
      className,
      onSubmit,
      isLoading = false,
      title = "PMS Asset Builder Dashboard Log In",
      subtitle,
      showForgotPassword = true,
      onForgotPassword,
      ...props
    },
    ref
  ) => {
    const [email, setEmail] = React.useState("");
    const [password, setPassword] = React.useState("");
    const [showPassword, setShowPassword] = React.useState(false);

    const handleSubmit = (e: React.FormEvent) => {
      e.preventDefault();
      onSubmit?.({ email, password });
    };

    return (
      <div
        ref={ref}
        className={cn("ui:w-full ui:max-w-md", className)}
        {...props}
      >
        <Card className="ui:w-full">
          <CardHeader className="ui:space-y-1 ui:text-center">
            <div className="ui:flex ui:justify-center ui:mb-4">
              {/* Logo placeholder - you can replace this with your actual logo */}
              <div className="ui:w-12 ui:h-12 ui:bg-primary ui:rounded-lg ui:flex ui:items-center ui:justify-center">
                <span className="ui:text-white ui:font-bold ui:text-xl">C</span>
              </div>
            </div>
            <CardTitle className="ui:text-xl ui:font-semibold ui:text-primary">
              {title}
            </CardTitle>
            {subtitle && (
              <p className="ui:text-sm ui:text-muted-foreground">{subtitle}</p>
            )}
          </CardHeader>
          <CardContent>
            <form onSubmit={handleSubmit} className="ui:space-y-4">
              <div className="ui:space-y-2">
                <Label htmlFor="email">Email Address</Label>
                <div className="ui:relative">
                  <Input
                    id="email"
                    type="email"
                    placeholder="Email Address"
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                    className="ui:pl-10"
                    required
                    disabled={isLoading}
                  />
                  <Icons.mail className="ui:absolute ui:left-3 ui:top-1/2 ui:transform ui:-translate-y-1/2 ui:h-4 ui:w-4 ui:text-muted-foreground" />
                </div>
              </div>

              <div className="ui:space-y-2">
                <Label htmlFor="password">Password</Label>
                <div className="ui:relative">
                  <Input
                    id="password"
                    type={showPassword ? "text" : "password"}
                    placeholder="Password"
                    value={password}
                    onChange={(e) => setPassword(e.target.value)}
                    className="ui:pl-10 ui:pr-10"
                    required
                    disabled={isLoading}
                  />
                  <Icons.lock className="ui:absolute ui:left-3 ui:top-1/2 ui:transform ui:-translate-y-1/2 ui:h-4 ui:w-4 ui:text-muted-foreground" />
                  <button
                    type="button"
                    onClick={() => setShowPassword(!showPassword)}
                    className="ui:absolute ui:right-3 ui:top-1/2 ui:transform ui:-translate-y-1/2 ui:text-muted-foreground hover:ui:text-foreground ui:transition-colors"
                    disabled={isLoading}
                  >
                    {showPassword ? (
                      <Icons.eyeOff className="ui:h-4 ui:w-4" />
                    ) : (
                      <Icons.eye className="ui:h-4 ui:w-4" />
                    )}
                  </button>
                </div>
              </div>

              <Button type="submit" className="ui:w-full" disabled={isLoading}>
                {isLoading ? (
                  <>
                    <Icons.spinner className="ui:mr-2 ui:h-4 ui:w-4 ui:animate-spin" />
                    Logging in...
                  </>
                ) : (
                  <>
                    Login
                    <Icons.arrowRight className="ui:ml-2 ui:h-4 ui:w-4" />
                  </>
                )}
              </Button>

              {showForgotPassword && (
                <div className="ui:text-center">
                  <button
                    type="button"
                    onClick={onForgotPassword}
                    className="ui:text-sm ui:text-muted-foreground hover:ui:text-foreground ui:underline ui:transition-colors"
                    disabled={isLoading}
                  >
                    Forgot Password?
                  </button>
                </div>
              )}
            </form>
          </CardContent>
        </Card>
      </div>
    );
  }
);
LoginForm.displayName = "LoginForm";

export { LoginForm };
