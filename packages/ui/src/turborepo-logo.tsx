export const TurborepoLogo = () => {
  return (
    <svg
      aria-label="Turbo logomark"
      height="80"
      role="img"
      viewBox="0 0 40 40"
      width="80"
    >
      <path
        d="M19.9845 6.99291C12.818 6.99291 6.98755 12.8279 6.98755 19.9999C6.98755 27.1721 12.818 33.0071 19.9845 33.0071C27.1509 33.0071 32.9814 27.1721 32.9814 19.9999C32.9814 12.8279 27.1509 6.99291 19.9845 6.99291ZM19.9845 26.7313C16.2694 26.7313 13.2585 23.718 13.2585 19.9999C13.2585 16.282 16.2694 13.2687 19.9845 13.2687C23.6996 13.2687 26.7105 16.282 26.7105 19.9999C26.7105 23.718 23.6996 26.7313 19.9845 26.7313Z"
        fill="currentcolor"
      ></path>
      <path
        clipRule="evenodd"
        d="M21.0734 4.85648V0C31.621 0.564369 40 9.30362 40 19.9999C40 30.6963 31.621 39.4332 21.0734 40V35.1435C28.9344 34.5815 35.1594 28.0078 35.1594 19.9999C35.1594 11.9922 28.9344 5.41843 21.0734 4.85648ZM8.52181 29.931C6.43794 27.5233 5.09469 24.4568 4.85508 21.09H0C0.251709 25.8011 2.13468 30.0763 5.08501 33.368L8.51938 29.931H8.52181ZM18.8951 40V35.1435C15.5285 34.9037 12.4644 33.5619 10.0587 31.4739L6.62435 34.9109C9.91593 37.866 14.1876 39.7481 18.8927 40H18.8951Z"
        fill="url(#:Sb:paint0_linear_902_224)"
        fillRule="evenodd"
      ></path>
      <defs>
        <linearGradient
          gradientUnits="userSpaceOnUse"
          id=":Sb:paint0_linear_902_224"
          x1="21.8576"
          x2="2.17018"
          y1="2.81244"
          y2="22.4844"
        >
          <stop stopColor="#0096FF"></stop>
          <stop offset="1" stopColor="#FF1E56"></stop>
        </linearGradient>
      </defs>
    </svg>
  );
};
