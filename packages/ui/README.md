# @repo/ui - shadcn/ui Components

This package contains reusable UI components built with shadcn/ui, Tailwind CSS, and Radix UI primitives.

## Components

### Button
A versatile button component with multiple variants and sizes.

```tsx
import { Button } from "@repo/ui/button"

<Button>Default</Button>
<Button variant="secondary">Secondary</Button>
<Button variant="outline">Outline</Button>
<Button variant="ghost">Ghost</Button>
<Button variant="destructive">Destructive</Button>
<Button size="sm">Small</Button>
<Button size="lg">Large</Button>
```

### Input
A styled input component for forms.

```tsx
import { Input } from "@repo/ui/input"

<Input type="email" placeholder="Enter your email" />
<Input type="password" placeholder="Enter your password" />
```

### Label
A label component that works with form inputs.

```tsx
import { Label } from "@repo/ui/label"

<Label htmlFor="email">Email Address</Label>
```

### Card
Card components for containing content.

```tsx
import { Card, CardHeader, CardTitle, CardDescription, CardContent, CardFooter } from "@repo/ui/card-ui"

<Card>
  <CardHeader>
    <CardTitle>Card Title</CardTitle>
    <CardDescription>Card description</CardDescription>
  </CardHeader>
  <CardContent>
    <p>Card content goes here</p>
  </CardContent>
  <CardFooter>
    <Button>Action</Button>
  </CardFooter>
</Card>
```

### Form
Form components for building structured forms.

```tsx
import { Form, FormField, FormLabel, FormControl } from "@repo/ui/form"

<Form>
  <FormField>
    <FormLabel htmlFor="name">Name</FormLabel>
    <FormControl>
      <Input id="name" placeholder="Your name" />
    </FormControl>
  </FormField>
</Form>
```

### Icons
Pre-configured icons from Lucide React.

```tsx
import { Icons } from "@repo/ui/icons"

<Icons.mail className="h-4 w-4" />
<Icons.lock className="h-4 w-4" />
<Icons.eye className="h-4 w-4" />
```

### LoginForm
A complete login form component matching your design.

```tsx
import { LoginForm } from "@repo/ui/login-form"

<LoginForm
  onSubmit={(data) => console.log(data)}
  onForgotPassword={() => console.log("Forgot password")}
  title="PMS Asset Builder Dashboard Log In"
  isLoading={false}
/>
```

## Usage in Apps

To use these components in your apps:

1. Import the component you need:
```tsx
import { Button } from "@repo/ui/button"
import { LoginForm } from "@repo/ui/login-form"
```

2. Make sure to import the styles in your app's layout:
```tsx
import "@repo/ui/styles.css"
```

## Development

To build the components:

```bash
cd packages/ui
pnpm run build:components  # Compile TypeScript
pnpm run build:styles     # Build CSS
```

To develop with watch mode:

```bash
pnpm run dev:components   # Watch TypeScript
pnpm run dev:styles       # Watch CSS
```

## Styling

All components use the `ui:` prefix for Tailwind classes to avoid conflicts. The components are designed to work with your existing Tailwind setup and can be customized by passing additional className props.

## Dependencies

- React 19+
- Tailwind CSS 4+
- Radix UI primitives
- Lucide React (for icons)
- class-variance-authority (for component variants)
- clsx & tailwind-merge (for class merging)
